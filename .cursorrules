# Next.js Frontend Coding Standards

Comprehensive coding standards for Next.js 14+ applications using App Router to ensure consistent, robust, flexible, scalable, maintainable, and secure code that follows industry best practices.

## TypeScript

- Use TypeScript with strict type checking for all files
- Define proper interfaces and types for props, state, and function returns
- Avoid using 'any' type; use proper typing or generic types instead
- Use TypeScript utility types (Pick, Omit, Partial, etc.) for type composition
- Define custom types in separate .types.ts files for complex applications

## Next.js App Router

- Use App Router structure with app/ directory for Next.js 14+
- Leverage Server Components by default; use Client Components only when necessary
- Use proper file conventions: page.tsx, layout.tsx, loading.tsx, error.tsx, not-found.tsx
- Implement nested layouts for shared UI across route groups
- Use route groups (folders) for organizing routes without affecting URL structure
- Implement proper metadata API for SEO optimization

## Component Structure

- Use functional components with React hooks
- Client components should be marked with 'use client' directive at the top
- Keep components focused on a single responsibility
- Organize complex components into smaller sub-components
- Use Server Components for data fetching and static content
- Implement proper component composition patterns

## Data Fetching

- Use native fetch with proper caching strategies in Server Components
- Implement proper error handling for data fetching operations
- Use Suspense boundaries for loading states
- Leverage Next.js built-in caching mechanisms (fetch cache, router cache)
- Use streaming and progressive enhancement where appropriate
- Implement proper revalidation strategies for dynamic data

## File Naming & Organization

- Use PascalCase for React components (e.g., NavBar.tsx, Button.tsx)
- Use kebab-case for utility files (e.g., search-form.tsx)
- Group related components in subdirectories (e.g., ui/, cards/, editor/)
- Use consistent import paths with @/ alias for src directory
- Follow Next.js App Router conventions for special files
- Organize API routes in app/api/ directory with proper RESTful structure

## Styling

- Use Tailwind CSS for styling with the class-variance-authority pattern
- Follow utility-first approach with composable classes
- Extract reusable UI patterns into components under src/components/ui/
- Use the cn() utility for conditional class merging
- Implement responsive design with mobile-first approach
- Use CSS custom properties for theming and dark mode support
-always make sure each component and page is mobile, tablet, laptop and large screen responsive

## Code Formatting

- Use 2-space indentation
- Use semicolons consistently
- Keep line length under 100 characters
- Use single quotes for strings
- Use trailing commas in multi-line objects and arrays
- Configure Prettier and ESLint for consistent formatting

## State Management

- Use React Hooks (useState, useEffect, useContext) for client-side state
- Implement custom hooks for reusable stateful logic
- Keep state as minimal and local as possible
- Use proper cleanup in useEffect hooks
- Consider Zustand or React Query for complex state management
- Use URL state for shareable application state

## Performance Optimization

- Implement useMemo and useCallback where appropriate
- Use proper key props in lists
- Implement proper memoization for expensive calculations
- Lazy load components and assets when possible
- Optimize image loading with next/image component
- Implement code splitting for route-based components
- Use proper caching strategies and revalidation
- Minimize JavaScript bundle size with tree shaking
- Implement proper prefetching for navigation

## SEO & Metadata

- Use Next.js Metadata API for dynamic meta tags
- Implement proper Open Graph and Twitter Card metadata
- Use semantic HTML structure for better SEO
- Implement structured data where appropriate
- Generate proper sitemaps and robots.txt
- Ensure proper canonical URLs for content

## Error Handling

- Implement proper error boundaries for client components
- Use error.tsx files for route-level error handling
- Use try/catch blocks for async operations
- Include proper error feedback for users
- Handle loading and error states in data fetching operations
- Implement proper logging for production error tracking

## API Routes

- Use proper HTTP methods and status codes
- Implement request validation and sanitization
- Use proper error responses with consistent structure
- Implement rate limiting for production APIs
- Use proper authentication and authorization middleware
- Document API endpoints with proper TypeScript types

## Security

- Validate and sanitize all user inputs
- Use environment variables for sensitive configuration
- Implement proper CSRF protection
- Use HTTPS in production with proper headers
- Implement proper authentication and authorization
- Follow OWASP security guidelines for web applications
- Sanitize data from external sources and APIs

## Environment & Configuration

- Use .env.local for local development secrets
- Use .env.example to document required environment variables
- Validate environment variables at build time
- Use different configurations for development, staging, and production
- Never commit sensitive data to version control
- Use proper type safety for environment variables

## Accessibility

- Ensure proper ARIA attributes and landmarks
- Use semantic HTML elements
- Implement keyboard navigation and focus management
- Ensure sufficient color contrast ratios
- Test with screen readers and accessibility tools
- Implement proper form validation and error messages
- Use proper heading hierarchy

## Testing

- Write unit tests for components and utilities
- Implement integration tests for complex user flows
- Mock external dependencies and API calls
- Test accessibility compliance with automated tools
- Use React Testing Library for component testing
- Implement E2E tests for critical user journeys

## Documentation

- Add JSDoc comments for functions and components
- Document component props with meaningful descriptions
- Include usage examples for complex components
- Document API routes with proper request/response examples
- Maintain README with setup and development instructions
- Document deployment and environment setup procedures

## Code Quality

- Follow ESLint and Prettier rules consistently
- Avoid code duplication by extracting common functionality
- Use meaningful variable and function names
- Keep functions small and focused on single responsibilities
- Implement proper code reviews and quality gates
- Use TypeScript strict mode for better type safety

## Dependencies & Build

- Keep dependencies updated but use stable versions
- Use exact versions for critical dependencies
- Prefer lightweight and well-maintained libraries
- Audit dependencies regularly for security vulnerabilities
- Use proper dependency separation (dependencies vs devDependencies)
- Optimize bundle size with proper tree shaking and code splitting

## Development Workflow

- Use proper Git commit message conventions
- Implement pre-commit hooks for code quality checks
- Use feature branches and proper merge strategies
- Implement proper CI/CD pipelines for testing and deployment
- Use proper versioning and release management
- Document breaking changes and migration guides 